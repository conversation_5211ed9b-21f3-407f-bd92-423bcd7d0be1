﻿  main.c
C:\Userfile\ccode\BWcode\Invoke-x64dbg-loaddll\main.c(13,3): warning C4047: “函数”:“DWORD”与“void *”的间接级别不同
C:\Userfile\ccode\BWcode\Invoke-x64dbg-loaddll\main.c(13,3): warning C4024: “CreateFileW”: 形参和实参 6 的类型不同
C:\Userfile\ccode\BWcode\Invoke-x64dbg-loaddll\main.c(55,14): warning C4047: “==”:“BOOL”与“void *”的间接级别不同
  正在生成代码
  Previous IPDB not found, fall back to full compilation.
  All 2 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  已完成代码的生成
  Invoke-x64dbg-loaddll.vcxproj -> C:\Userfile\ccode\BWcode\x64\Release\Invoke-x64dbg-loaddll.exe
